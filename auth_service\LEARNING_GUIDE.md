# 🎓 Hướng dẫn Học tập: Auth Service Microservice

Đây là tài liệu học tập chi tiết giải thích các khái niệm và patterns được sử dụng trong auth_service microservice.

## 📚 Mục lục

1. [Kiến trúc Microservice](#kiến-trúc-microservice)
2. [Flask Application Factory Pattern](#flask-application-factory-pattern)
3. [Database Models với SQLAlchemy](#database-models-với-sqlalchemy)
4. [JWT Authentication](#jwt-authentication)
5. [API Design Patterns](#api-design-patterns)
6. [Testing Strategies](#testing-strategies)
7. [Security Best Practices](#security-best-practices)

## 🏗️ Kiến trúc Microservice

### Tại sao sử dụng Microservice?

**Microservice** là architectural pattern chia ứng dụng thành các services nhỏ, độc lập:

```
Monolithic App          vs          Microservices
┌─────────────────┐                ┌──────────────┐  ┌──────────────┐
│                 │                │ Auth Service │  │ User Service │
│   All Features  │                │              │  │              │
│                 │                └──────────────┘  └──────────────┘
│                 │                ┌──────────────┐  ┌──────────────┐
└─────────────────┘                │ File Service │  │ API Gateway  │
                                   │              │  │              │
                                   └──────────────┘  └──────────────┘
```

**Ưu điểm:**
- **Scalability**: Scale từng service riêng biệt
- **Technology Diversity**: Mỗi service có thể dùng tech stack khác nhau
- **Team Independence**: Teams có thể develop độc lập
- **Fault Isolation**: Lỗi ở 1 service không crash toàn bộ system

### Cấu trúc Thư mục

```
auth_service/
├── app/                    # 📁 Application Layer
│   ├── models/            # 🗄️  Data Models (Database)
│   ├── routes/            # 🛣️  API Endpoints
│   ├── services/          # 🔧 Business Logic
│   ├── utils/             # 🛠️  Helper Functions
│   └── middleware/        # 🔒 Request Processing
├── config/                # ⚙️  Configuration
├── tests/                 # 🧪 Unit Tests
└── migrations/           # 📊 Database Migrations
```

## 🏭 Flask Application Factory Pattern

### Tại sao sử dụng Application Factory?

**Application Factory Pattern** cho phép tạo multiple Flask app instances với configs khác nhau:

<augment_code_snippet path="auth_service/app/__init__.py" mode="EXCERPT">
````python
def create_app(config_class=Config):
    """
    Application Factory Function
    Tạo và cấu hình Flask application instance.
    """
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Initialize extensions
    db.init_app(app)
    jwt.init_app(app)
    
    return app
````
</augment_code_snippet>

**Lợi ích:**
- **Testing**: Tạo app instance riêng cho tests
- **Multiple Environments**: Dev, staging, production configs
- **Extension Management**: Initialize extensions properly

### Blueprint Pattern

**Blueprints** tổ chức routes thành modules:

<augment_code_snippet path="auth_service/app/routes/auth_routes.py" mode="EXCERPT">
````python
# Tạo Blueprint
auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/register', methods=['POST'])
def register():
    # Route logic here
    pass
````
</augment_code_snippet>

## 🗄️ Database Models với SQLAlchemy

### ORM (Object-Relational Mapping)

**SQLAlchemy ORM** map Python classes thành database tables:

<augment_code_snippet path="auth_service/app/models/user.py" mode="EXCERPT">
````python
class User(db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
````
</augment_code_snippet>

**Các khái niệm quan trọng:**

1. **Model Class**: Đại diện cho database table
2. **Columns**: Định nghĩa fields và constraints
3. **Relationships**: Liên kết giữa các tables
4. **Methods**: Business logic methods

### Database Migrations

**Flask-Migrate** quản lý database schema changes:

```bash
flask db init        # Khởi tạo migration repository
flask db migrate     # Tạo migration script
flask db upgrade     # Apply migrations
```

## 🔐 JWT Authentication

### JWT (JSON Web Token) là gì?

**JWT** là standard để truyền thông tin an toàn giữa parties:

```
Header.Payload.Signature
eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

**Cấu trúc:**
- **Header**: Algorithm và token type
- **Payload**: Claims (user data)
- **Signature**: Verify token integrity

### Access vs Refresh Tokens

```python
# Access Token: Short-lived (1 hour)
access_token = create_access_token(identity=user.id)

# Refresh Token: Long-lived (30 days)
refresh_token = create_refresh_token(identity=user.id)
```

**Flow:**
1. User login → Receive both tokens
2. Use access token cho API calls
3. Access token expires → Use refresh token để get new access token
4. Refresh token expires → User phải login lại

## 🛣️ API Design Patterns

### RESTful API Design

**REST** (Representational State Transfer) principles:

| Method | Endpoint | Purpose |
|--------|----------|---------|
| POST | /api/auth/register | Tạo user mới |
| POST | /api/auth/login | Authenticate user |
| GET | /api/auth/me | Get current user |
| PUT | /api/users/profile | Update user profile |

### Request/Response Patterns

**Consistent Response Format:**

```json
{
    "message": "Success message",
    "data": { ... },
    "error": "Error message (if any)",
    "code": "ERROR_CODE"
}
```

### Input Validation với Marshmallow

**Marshmallow schemas** validate và serialize data:

<augment_code_snippet path="auth_service/app/routes/auth_routes.py" mode="EXCERPT">
````python
class RegisterSchema(Schema):
    username = fields.Str(required=True, validate=lambda x: len(x) >= 3)
    email = fields.Email(required=True)
    password = fields.Str(required=True, validate=lambda x: len(x) >= 6)
````
</augment_code_snippet>

## 🧪 Testing Strategies

### Test Structure

**Pytest** với fixtures cho setup/teardown:

<augment_code_snippet path="auth_service/tests/conftest.py" mode="EXCERPT">
````python
@pytest.fixture
def app():
    """Fixture tạo Flask app cho testing"""
    app = create_app(TestingConfig)
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()
````
</augment_code_snippet>

### Test Categories

1. **Unit Tests**: Test individual functions
2. **Integration Tests**: Test API endpoints
3. **Functional Tests**: Test complete workflows

### Test-Driven Development (TDD)

**TDD Cycle:**
1. **Red**: Write failing test
2. **Green**: Write minimal code để pass test
3. **Refactor**: Improve code quality

## 🔒 Security Best Practices

### Password Security

```python
# Hash passwords với bcrypt
from werkzeug.security import generate_password_hash, check_password_hash

def set_password(self, password):
    self.password_hash = generate_password_hash(password)

def check_password(self, password):
    return check_password_hash(self.password_hash, password)
```

### Input Validation

**Validate tất cả user inputs:**
- Email format validation
- Password strength requirements
- Username format rules
- Sanitize inputs để prevent XSS

### Security Headers

```python
def security_headers_middleware():
    def after_request(response):
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        return response
    return after_request
```

## 🚀 Deployment Considerations

### Environment Configuration

**Separate configs cho different environments:**
- Development: Debug enabled, SQLite database
- Testing: In-memory database, disabled features
- Production: Security optimized, production database

### Docker Containerization

**Benefits:**
- Consistent environment across dev/prod
- Easy scaling và deployment
- Isolation từ host system

### Monitoring và Logging

**Essential monitoring:**
- Request/response logging
- Error tracking
- Performance metrics
- Health checks

## 📈 Scaling Considerations

### Database Optimization

- **Indexing**: Add indexes cho frequently queried columns
- **Connection Pooling**: Reuse database connections
- **Query Optimization**: Avoid N+1 queries

### Caching Strategies

- **Redis**: Cache frequently accessed data
- **Application-level caching**: Cache expensive computations
- **CDN**: Cache static assets

### Load Balancing

**Distribute traffic across multiple instances:**
- Horizontal scaling (more servers)
- Load balancer routing
- Session management considerations

## 🎯 Next Steps

1. **Implement advanced features:**
   - Email verification
   - Password reset
   - Two-factor authentication
   - Role-based permissions

2. **Add monitoring:**
   - Logging framework
   - Metrics collection
   - Health checks
   - Error tracking

3. **Performance optimization:**
   - Database indexing
   - Caching layer
   - API rate limiting
   - Response compression

4. **Security enhancements:**
   - Token blacklisting
   - Brute force protection
   - Input sanitization
   - Security auditing
