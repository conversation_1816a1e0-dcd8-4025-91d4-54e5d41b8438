"""
Tests for Authentication Routes

Đây là file test cho các authentication endpoints.
Test các scenarios: success cases, error cases, edge cases.
"""

import pytest
import json
from app.models.user import User


class TestRegisterEndpoint:
    """
    Test class cho /api/auth/register endpoint
    """
    
    def test_register_success(self, client, sample_user_data):
        """
        Test successful user registration
        """
        response = client.post('/api/auth/register', json=sample_user_data)
        
        # Check response status
        assert response.status_code == 201
        
        # Check response data
        data = response.get_json()
        assert 'message' in data
        assert 'user' in data
        assert 'access_token' in data
        assert 'refresh_token' in data
        
        # Check user data
        user_data = data['user']
        assert user_data['username'] == sample_user_data['username']
        assert user_data['email'] == sample_user_data['email']
        assert 'password_hash' not in user_data  # Password không được return
    
    def test_register_duplicate_username(self, client, sample_user_data, create_user):
        """
        Test registration với username đã tồn tại
        """
        response = client.post('/api/auth/register', json=sample_user_data)
        
        assert response.status_code == 400
        data = response.get_json()
        assert 'Username already exists' in data['error']
    
    def test_register_duplicate_email(self, client, sample_user_data, create_user):
        """
        Test registration với email đã tồn tại
        """
        # Thay đổi username nhưng giữ email
        new_data = sample_user_data.copy()
        new_data['username'] = 'newuser'
        
        response = client.post('/api/auth/register', json=new_data)
        
        assert response.status_code == 400
        data = response.get_json()
        assert 'Email already exists' in data['error']
    
    def test_register_invalid_data(self, client):
        """
        Test registration với invalid data
        """
        invalid_data = {
            'username': 'ab',  # Too short
            'email': 'invalid-email',  # Invalid email
            'password': '123'  # Too short
        }
        
        response = client.post('/api/auth/register', json=invalid_data)
        
        assert response.status_code == 400
        data = response.get_json()
        assert 'Validation failed' in data['error']
    
    def test_register_missing_required_fields(self, client):
        """
        Test registration với missing required fields
        """
        incomplete_data = {
            'username': 'testuser'
            # Missing email and password
        }
        
        response = client.post('/api/auth/register', json=incomplete_data)
        
        assert response.status_code == 400


class TestLoginEndpoint:
    """
    Test class cho /api/auth/login endpoint
    """
    
    def test_login_success(self, client, create_user):
        """
        Test successful login
        """
        login_data = {
            'username': create_user.username,
            'password': 'TestPassword123!'
        }
        
        response = client.post('/api/auth/login', json=login_data)
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'message' in data
        assert 'user' in data
        assert 'access_token' in data
        assert 'refresh_token' in data
    
    def test_login_with_email(self, client, create_user):
        """
        Test login với email thay vì username
        """
        login_data = {
            'username': create_user.email,  # Sử dụng email
            'password': 'TestPassword123!'
        }
        
        response = client.post('/api/auth/login', json=login_data)
        
        assert response.status_code == 200
    
    def test_login_invalid_password(self, client, create_user):
        """
        Test login với wrong password
        """
        login_data = {
            'username': create_user.username,
            'password': 'WrongPassword'
        }
        
        response = client.post('/api/auth/login', json=login_data)
        
        assert response.status_code == 401
        data = response.get_json()
        assert 'Invalid username or password' in data['error']
    
    def test_login_nonexistent_user(self, client):
        """
        Test login với user không tồn tại
        """
        login_data = {
            'username': 'nonexistent',
            'password': 'password123'
        }
        
        response = client.post('/api/auth/login', json=login_data)
        
        assert response.status_code == 401
    
    def test_login_inactive_user(self, client, create_user):
        """
        Test login với inactive user
        """
        # Deactivate user
        create_user.is_active = False
        
        login_data = {
            'username': create_user.username,
            'password': 'TestPassword123!'
        }
        
        response = client.post('/api/auth/login', json=login_data)
        
        assert response.status_code == 401
        data = response.get_json()
        assert 'Account is deactivated' in data['error']


class TestRefreshEndpoint:
    """
    Test class cho /api/auth/refresh endpoint
    """
    
    def test_refresh_token_success(self, client, create_user):
        """
        Test successful token refresh
        """
        # First login to get refresh token
        login_data = {
            'username': create_user.username,
            'password': 'TestPassword123!'
        }
        
        login_response = client.post('/api/auth/login', json=login_data)
        refresh_token = login_response.get_json()['refresh_token']
        
        # Use refresh token to get new access token
        headers = {'Authorization': f'Bearer {refresh_token}'}
        response = client.post('/api/auth/refresh', headers=headers)
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'access_token' in data
    
    def test_refresh_token_invalid(self, client):
        """
        Test refresh với invalid token
        """
        headers = {'Authorization': 'Bearer invalid_token'}
        response = client.post('/api/auth/refresh', headers=headers)
        
        assert response.status_code == 422  # JWT decode error


class TestLogoutEndpoint:
    """
    Test class cho /api/auth/logout endpoint
    """
    
    def test_logout_success(self, client, auth_headers):
        """
        Test successful logout
        """
        response = client.post('/api/auth/logout', headers=auth_headers)
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'Logout successful' in data['message']
    
    def test_logout_without_token(self, client):
        """
        Test logout without authentication token
        """
        response = client.post('/api/auth/logout')
        
        assert response.status_code == 401


class TestGetCurrentUserEndpoint:
    """
    Test class cho /api/auth/me endpoint
    """
    
    def test_get_current_user_success(self, client, auth_headers, create_user):
        """
        Test get current user profile
        """
        response = client.get('/api/auth/me', headers=auth_headers)
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'user' in data
        assert data['user']['username'] == create_user.username
    
    def test_get_current_user_without_token(self, client):
        """
        Test get current user without authentication
        """
        response = client.get('/api/auth/me')
        
        assert response.status_code == 401
