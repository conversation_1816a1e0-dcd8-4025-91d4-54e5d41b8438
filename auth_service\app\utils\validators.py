"""
Validation Utilities

Đ<PERSON>y là file chứa các utility functions để validate data.
Tách biệt validation logic để có thể reuse ở nhiều nơi.
"""

import re
from marshmallow import ValidationError


def validate_password_strength(password):
    """
    Validate password strength
    
    Requirements:
    - Ít nhất 8 ký tự
    - Có ít nhất 1 chữ hoa
    - <PERSON><PERSON> ít nhất 1 chữ thường  
    - Có ít nhất 1 số
    - Có ít nhất 1 ký tự đặc biệt
    
    Args:
        password (str): Password để validate
        
    Raises:
        ValidationError: Nếu password không đủ mạnh
    """
    if len(password) < 8:
        raise ValidationError('Password must be at least 8 characters long')
    
    if not re.search(r'[A-Z]', password):
        raise ValidationError('Password must contain at least one uppercase letter')
    
    if not re.search(r'[a-z]', password):
        raise ValidationError('Password must contain at least one lowercase letter')
    
    if not re.search(r'\d', password):
        raise ValidationError('Password must contain at least one number')
    
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        raise ValidationError('Password must contain at least one special character')


def validate_username(username):
    """
    Validate username format
    
    Requirements:
    - 3-30 ký tự
    - Chỉ chứa letters, numbers, underscore, hyphen
    - Không bắt đầu hoặc kết thúc bằng underscore/hyphen
    
    Args:
        username (str): Username để validate
        
    Raises:
        ValidationError: Nếu username không hợp lệ
    """
    if len(username) < 3 or len(username) > 30:
        raise ValidationError('Username must be between 3 and 30 characters')
    
    if not re.match(r'^[a-zA-Z0-9_-]+$', username):
        raise ValidationError('Username can only contain letters, numbers, underscore, and hyphen')
    
    if username.startswith(('_', '-')) or username.endswith(('_', '-')):
        raise ValidationError('Username cannot start or end with underscore or hyphen')


def validate_phone_number(phone):
    """
    Validate phone number format (Vietnamese format)
    
    Args:
        phone (str): Phone number để validate
        
    Raises:
        ValidationError: Nếu phone number không hợp lệ
    """
    if not phone:
        return  # Phone là optional
    
    # Remove spaces và dashes
    clean_phone = re.sub(r'[\s-]', '', phone)
    
    # Vietnamese phone number patterns
    patterns = [
        r'^(\+84|84|0)(3|5|7|8|9)[0-9]{8}$',  # Mobile numbers
        r'^(\+84|84|0)(2)[0-9]{9}$'           # Landline numbers
    ]
    
    if not any(re.match(pattern, clean_phone) for pattern in patterns):
        raise ValidationError('Invalid Vietnamese phone number format')


def validate_name(name, field_name):
    """
    Validate name fields (first_name, last_name)
    
    Args:
        name (str): Name để validate
        field_name (str): Tên field để hiển thị trong error message
        
    Raises:
        ValidationError: Nếu name không hợp lệ
    """
    if not name:
        return  # Name fields là optional
    
    if len(name) < 1 or len(name) > 50:
        raise ValidationError(f'{field_name} must be between 1 and 50 characters')
    
    # Chỉ cho phép letters, spaces, và một số ký tự đặc biệt
    if not re.match(r'^[a-zA-ZÀ-ỹ\s\'-]+$', name):
        raise ValidationError(f'{field_name} can only contain letters, spaces, apostrophes, and hyphens')


def validate_email_domain(email, allowed_domains=None):
    """
    Validate email domain (optional - cho enterprise use cases)
    
    Args:
        email (str): Email để validate
        allowed_domains (list): List các domain được phép (None = allow all)
        
    Raises:
        ValidationError: Nếu domain không được phép
    """
    if not allowed_domains:
        return  # Allow all domains
    
    domain = email.split('@')[1].lower()
    
    if domain not in [d.lower() for d in allowed_domains]:
        raise ValidationError(f'Email domain must be one of: {", ".join(allowed_domains)}')


def sanitize_input(text):
    """
    Sanitize user input để prevent XSS và injection attacks
    
    Args:
        text (str): Text để sanitize
        
    Returns:
        str: Sanitized text
    """
    if not text:
        return text
    
    # Remove HTML tags
    text = re.sub(r'<[^>]+>', '', text)
    
    # Remove script tags content
    text = re.sub(r'<script.*?</script>', '', text, flags=re.DOTALL | re.IGNORECASE)
    
    # Strip whitespace
    text = text.strip()
    
    return text


def validate_role(role):
    """
    Validate user role
    
    Args:
        role (str): Role để validate
        
    Raises:
        ValidationError: Nếu role không hợp lệ
    """
    allowed_roles = ['user', 'admin', 'moderator']
    
    if role not in allowed_roles:
        raise ValidationError(f'Role must be one of: {", ".join(allowed_roles)}')


# Custom Marshmallow validators
def length_validator(min_length=None, max_length=None):
    """
    Tạo length validator cho Marshmallow fields
    
    Args:
        min_length (int): Minimum length
        max_length (int): Maximum length
        
    Returns:
        function: Validator function
    """
    def validator(value):
        if min_length and len(value) < min_length:
            raise ValidationError(f'Must be at least {min_length} characters long')
        if max_length and len(value) > max_length:
            raise ValidationError(f'Must be no more than {max_length} characters long')
    
    return validator
