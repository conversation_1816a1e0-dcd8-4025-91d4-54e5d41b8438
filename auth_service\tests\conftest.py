"""
Pytest Configuration File

Đ<PERSON><PERSON> là file cấu hình cho pytest, chứa các fixtures được share giữa các test files.
Fixtures là các functions setup data hoặc objects cho tests.
"""

import pytest
from app import create_app, db
from app.models.user import User
from config.config import TestingConfig


@pytest.fixture
def app():
    """
    Fixture tạo Flask app instance cho testing
    
    Returns:
        Flask: Flask app configured for testing
    """
    # Tạo app với testing configuration
    app = create_app(TestingConfig)
    
    # Setup application context
    with app.app_context():
        # Tạo database tables
        db.create_all()
        
        yield app
        
        # Cleanup sau khi test xong
        db.session.remove()
        db.drop_all()


@pytest.fixture
def client(app):
    """
    Fixture tạo test client
    
    Args:
        app: Flask app fixture
        
    Returns:
        FlaskClient: Test client để make HTTP requests
    """
    return app.test_client()


@pytest.fixture
def runner(app):
    """
    Fixture tạo CLI runner
    
    Args:
        app: Flask app fixture
        
    Returns:
        FlaskCliRunner: CLI runner để test CLI commands
    """
    return app.test_cli_runner()


@pytest.fixture
def sample_user_data():
    """
    Fixture chứa sample user data cho tests
    
    Returns:
        dict: Sample user data
    """
    return {
        'username': 'testuser',
        'email': '<EMAIL>',
        'password': 'TestPassword123!',
        'first_name': 'Test',
        'last_name': 'User',
        'phone': '0123456789'
    }


@pytest.fixture
def create_user(app, sample_user_data):
    """
    Fixture tạo user trong database
    
    Args:
        app: Flask app fixture
        sample_user_data: Sample user data fixture
        
    Returns:
        User: Created user object
    """
    with app.app_context():
        user = User(
            username=sample_user_data['username'],
            email=sample_user_data['email'],
            password=sample_user_data['password'],
            first_name=sample_user_data['first_name'],
            last_name=sample_user_data['last_name'],
            phone=sample_user_data['phone']
        )
        
        db.session.add(user)
        db.session.commit()
        
        return user


@pytest.fixture
def admin_user(app):
    """
    Fixture tạo admin user
    
    Args:
        app: Flask app fixture
        
    Returns:
        User: Admin user object
    """
    with app.app_context():
        admin = User(
            username='admin',
            email='<EMAIL>',
            password='AdminPassword123!',
            first_name='Admin',
            last_name='User',
            role='admin'
        )
        
        db.session.add(admin)
        db.session.commit()
        
        return admin


@pytest.fixture
def auth_headers(client, create_user):
    """
    Fixture tạo authentication headers với valid JWT token
    
    Args:
        client: Test client fixture
        create_user: User fixture
        
    Returns:
        dict: Headers với Authorization token
    """
    # Login để get token
    login_data = {
        'username': create_user.username,
        'password': 'TestPassword123!'
    }
    
    response = client.post('/api/auth/login', json=login_data)
    token = response.json['access_token']
    
    return {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }


@pytest.fixture
def admin_headers(client, admin_user):
    """
    Fixture tạo admin authentication headers
    
    Args:
        client: Test client fixture
        admin_user: Admin user fixture
        
    Returns:
        dict: Headers với admin Authorization token
    """
    # Login admin để get token
    login_data = {
        'username': admin_user.username,
        'password': 'AdminPassword123!'
    }
    
    response = client.post('/api/auth/login', json=login_data)
    token = response.json['access_token']
    
    return {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
