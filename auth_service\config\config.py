"""
Configuration Classes

<PERSON><PERSON><PERSON> là file chứa các class cấu hình cho different environments.
Sử dụng class-based configuration để dễ dàng switch giữa các môi trường.
"""

import os
from datetime import timedelta


class Config:
    """
    Base Configuration Class
    
    Chứa các cấu hình chung cho tất cả environments.
    Các class con sẽ inherit và override các giá trị cần thiết.
    """
    
    # Flask Secret Key - dùng để sign sessions và cookies
    # QUAN TRỌNG: Trong production phải set từ environment variable
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # JWT Configuration
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-change-in-production'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=1)  # Access token hết hạn sau 1 giờ
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)  # Refresh token hết hạn sau 30 ngày
    
    # Database Configuration
    # SQLite default cho development (dễ setup)
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///auth_service.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False  # Tắt để tiết kiệm memory
    
    # CORS Configuration
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', '*').split(',')
    
    # Pagination
    POSTS_PER_PAGE = 20
    
    # Email Configuration (cho password reset, verification, etc.)
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')


class DevelopmentConfig(Config):
    """
    Development Environment Configuration
    
    Cấu hình cho môi trường development - ưu tiên debugging và ease of use.
    """
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or 'sqlite:///auth_dev.db'


class TestingConfig(Config):
    """
    Testing Environment Configuration
    
    Cấu hình cho môi trường testing - sử dụng in-memory database.
    """
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'  # In-memory database cho tests
    WTF_CSRF_ENABLED = False  # Tắt CSRF protection trong tests


class ProductionConfig(Config):
    """
    Production Environment Configuration
    
    Cấu hình cho môi trường production - ưu tiên security và performance.
    """
    DEBUG = False
    
    # Production database - thường là PostgreSQL hoặc SQL Server
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///auth_production.db'  # Fallback (không nên dùng SQLite trong production)
    
    # Stricter CORS trong production
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', 'https://yourdomain.com').split(',')


# Dictionary để dễ dàng select config class
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
