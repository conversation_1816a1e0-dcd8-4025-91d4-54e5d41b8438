"""
User Model

Đ<PERSON><PERSON> là SQLAlchemy model cho User entity.
Model này định nghĩa cấu trúc database table và các methods liên quan đến User.
"""

from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from app import db


class User(db.Model):
    """
    User Model Class
    
    Đại diện cho bảng users trong database.
    Chứa thông tin user và các methods để xử lý authentication.
    """
    
    # Tên bảng trong database
    __tablename__ = 'users'
    
    # Primary Key
    id = db.Column(db.Integer, primary_key=True)
    
    # User Information
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    
    # Profile Information
    first_name = db.Column(db.String(50), nullable=True)
    last_name = db.Column(db.String(50), nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    
    # Account Status
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    is_verified = db.Column(db.Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime, nullable=True)
    
    # Role-based Access Control (simple approach)
    role = db.Column(db.String(20), default='user', nullable=False)  # user, admin, moderator
    
    def __init__(self, username, email, password, **kwargs):
        """
        Constructor cho User model
        
        Args:
            username (str): Tên đăng nhập unique
            email (str): Email address unique
            password (str): Plain text password (sẽ được hash)
            **kwargs: Các field optional khác
        """
        self.username = username
        self.email = email
        self.set_password(password)  # Hash password trước khi lưu
        
        # Set optional fields nếu có
        self.first_name = kwargs.get('first_name')
        self.last_name = kwargs.get('last_name')
        self.phone = kwargs.get('phone')
        self.role = kwargs.get('role', 'user')
    
    def set_password(self, password):
        """
        Hash và lưu password
        
        Args:
            password (str): Plain text password
        """
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """
        Kiểm tra password có đúng không
        
        Args:
            password (str): Plain text password để check
            
        Returns:
            bool: True nếu password đúng, False nếu sai
        """
        return check_password_hash(self.password_hash, password)
    
    def update_last_login(self):
        """
        Cập nhật thời gian login cuối cùng
        """
        self.last_login = datetime.utcnow()
        db.session.commit()
    
    def to_dict(self, include_sensitive=False):
        """
        Convert User object thành dictionary
        
        Args:
            include_sensitive (bool): Có include sensitive data không
            
        Returns:
            dict: User data as dictionary
        """
        data = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'phone': self.phone,
            'is_active': self.is_active,
            'is_verified': self.is_verified,
            'role': self.role,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
        
        # Chỉ include sensitive data khi cần thiết
        if include_sensitive:
            data['password_hash'] = self.password_hash
            
        return data
    
    @staticmethod
    def find_by_username(username):
        """
        Tìm user theo username
        
        Args:
            username (str): Username để tìm
            
        Returns:
            User: User object hoặc None nếu không tìm thấy
        """
        return User.query.filter_by(username=username).first()
    
    @staticmethod
    def find_by_email(email):
        """
        Tìm user theo email
        
        Args:
            email (str): Email để tìm
            
        Returns:
            User: User object hoặc None nếu không tìm thấy
        """
        return User.query.filter_by(email=email).first()
    
    @staticmethod
    def find_by_id(user_id):
        """
        Tìm user theo ID
        
        Args:
            user_id (int): User ID để tìm
            
        Returns:
            User: User object hoặc None nếu không tìm thấy
        """
        return User.query.get(user_id)
    
    def __repr__(self):
        """
        String representation của User object
        """
        return f'<User {self.username}>'
