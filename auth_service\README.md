# Auth Service Microservice

Đây là microservice xử lý authentication và authorization cho hệ thống AI wound detection. Service này cung cấp các API endpoints để đăng ký, đ<PERSON><PERSON> nh<PERSON><PERSON>, quản lý user và JWT token authentication.

## 🏗️ Kiến trúc

```
auth_service/
├── app/                    # Application code
│   ├── __init__.py        # Flask app factory
│   ├── models/            # Database models
│   │   └── user.py        # User model
│   ├── routes/            # API routes
│   │   ├── auth_routes.py # Authentication endpoints
│   │   └── user_routes.py # User management endpoints
│   ├── services/          # Business logic
│   │   └── auth_service.py
│   ├── utils/             # Utility functions
│   │   ├── validators.py  # Input validation
│   │   └── jwt_helpers.py # JWT utilities
│   └── middleware/        # Custom middleware
│       └── auth_middleware.py
├── config/                # Configuration
│   └── config.py         # Environment configs
├── tests/                 # Unit tests
├── migrations/           # Database migrations
├── requirements.txt      # Python dependencies
├── .env.example         # Environment variables template
└── run.py              # Application entry point
```

## 🚀 Cài đặt và Chạy

### 1. Clone và Setup Environment

```bash
cd auth_service
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 2. Cấu hình Environment Variables

```bash
cp .env.example .env
# Chỉnh sửa .env với các giá trị thực tế
```

### 3. Khởi tạo Database

```bash
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

### 4. Chạy Development Server

```bash
python run.py
```

Server sẽ chạy tại `http://localhost:5000`

## 📚 API Documentation

### Authentication Endpoints

#### POST /api/auth/register
Đăng ký user mới

**Request Body:**
```json
{
    "username": "john_doe",
    "email": "<EMAIL>",
    "password": "password123",
    "first_name": "John",
    "last_name": "Doe",
    "phone": "0123456789"
}
```

**Response (201):**
```json
{
    "message": "User registered successfully",
    "user": {
        "id": 1,
        "username": "john_doe",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "is_active": true,
        "role": "user"
    },
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

#### POST /api/auth/login
Đăng nhập user

**Request Body:**
```json
{
    "username": "john_doe",
    "password": "password123"
}
```

**Response (200):**
```json
{
    "message": "Login successful",
    "user": { ... },
    "access_token": "...",
    "refresh_token": "..."
}
```

#### POST /api/auth/refresh
Refresh access token

**Headers:**
```
Authorization: Bearer <refresh_token>
```

**Response (200):**
```json
{
    "access_token": "new_access_token"
}
```

#### POST /api/auth/logout
Đăng xuất user

**Headers:**
```
Authorization: Bearer <access_token>
```

#### GET /api/auth/me
Lấy thông tin user hiện tại

**Headers:**
```
Authorization: Bearer <access_token>
```

### User Management Endpoints

#### GET /api/users/profile
Lấy profile của user hiện tại

#### PUT /api/users/profile
Cập nhật profile

#### POST /api/users/change-password
Thay đổi password

#### POST /api/users/deactivate
Deactivate account

## 🧪 Testing

### Chạy Tests

```bash
# Chạy tất cả tests
pytest

# Chạy với coverage
pytest --cov=app

# Chạy specific test file
pytest tests/test_auth_routes.py

# Chạy với verbose output
pytest -v
```

### Test Structure

- `tests/conftest.py` - Pytest fixtures và configuration
- `tests/test_auth_routes.py` - Tests cho authentication endpoints
- `tests/test_user_routes.py` - Tests cho user management endpoints
- `tests/test_models.py` - Tests cho database models

## 🔒 Security Features

- **Password Hashing**: Sử dụng bcrypt để hash passwords
- **JWT Authentication**: Access và refresh tokens
- **Input Validation**: Marshmallow schemas cho validation
- **Rate Limiting**: Basic rate limiting middleware
- **CORS**: Configured cho cross-origin requests
- **Security Headers**: X-Frame-Options, X-XSS-Protection, etc.

## 🗄️ Database Schema

### Users Table

| Column | Type | Description |
|--------|------|-------------|
| id | Integer | Primary key |
| username | String(80) | Unique username |
| email | String(120) | Unique email |
| password_hash | String(255) | Hashed password |
| first_name | String(50) | First name |
| last_name | String(50) | Last name |
| phone | String(20) | Phone number |
| is_active | Boolean | Account status |
| is_verified | Boolean | Email verification status |
| role | String(20) | User role (user, admin, moderator) |
| created_at | DateTime | Creation timestamp |
| updated_at | DateTime | Last update timestamp |
| last_login | DateTime | Last login timestamp |

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| FLASK_ENV | Environment (development/production) | development |
| SECRET_KEY | Flask secret key | - |
| JWT_SECRET_KEY | JWT signing key | - |
| DATABASE_URL | Database connection string | sqlite:///auth_service.db |
| CORS_ORIGINS | Allowed CORS origins | * |

### Database Support

- **SQLite** (Development)
- **PostgreSQL** (Production recommended)
- **SQL Server** (Enterprise)
- **MySQL** (Alternative)

## 📈 Monitoring và Logging

- Request logging middleware
- Error handling và logging
- Health check endpoint: `GET /health`

## 🚀 Deployment

### Production Checklist

1. Set strong SECRET_KEY và JWT_SECRET_KEY
2. Use production database (PostgreSQL/SQL Server)
3. Configure proper CORS origins
4. Enable HTTPS
5. Set up monitoring và logging
6. Configure rate limiting
7. Set up database backups

### Docker Deployment

```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "run:app"]
```

## 🤝 Contributing

1. Fork repository
2. Create feature branch
3. Write tests cho new features
4. Ensure all tests pass
5. Submit pull request

## 📝 License

MIT License
