"""
Flask Application Factory Pattern

Đây là file khởi tạo Flask app sử dụng Application Factory Pattern.
Pattern này cho phép tạo nhiều instance của app với các cấu hình khác nhau
(development, testing, production).
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_jwt_extended import JWTManager
from flask_cors import CORS
from config.config import Config

# Khởi tạo các extension (nhưng chưa bind với app)
# Điều này cho phép chúng ta sử dụng extensions trong các module khác
db = SQLAlchemy()  # Database ORM
migrate = Migrate()  # Database migration tool
jwt = JWTManager()  # JWT token management
cors = CORS()  # Cross-Origin Resource Sharing


def create_app(config_class=Config):
    """
    Application Factory Function
    
    Tạo và cấu hình Flask application instance.
    
    Args:
        config_class: Class chứa cấu hình cho app (default: Config)
        
    Returns:
        Flask: Configured Flask application instance
    """
    
    # Tạo Flask app instance
    app = Flask(__name__)
    
    # Load cấu hình từ config class
    app.config.from_object(config_class)
    
    # Khởi tạo extensions với app
    db.init_app(app)  # Kết nối SQLAlchemy với app
    migrate.init_app(app, db)  # Kết nối Flask-Migrate với app và db
    jwt.init_app(app)  # Kết nối JWT manager với app
    cors.init_app(app)  # Enable CORS cho app
    
    # Import và đăng ký blueprints (routes)
    from app.routes.auth_routes import auth_bp
    from app.routes.user_routes import user_bp
    
    # Đăng ký blueprints với URL prefix
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(user_bp, url_prefix='/api/users')
    
    # Import models để SQLAlchemy biết về chúng
    from app.models import user
    
    # Tạo health check endpoint
    @app.route('/health')
    def health_check():
        """
        Health check endpoint để kiểm tra service có hoạt động không
        """
        return {'status': 'healthy', 'service': 'auth_service'}, 200
    
    return app
