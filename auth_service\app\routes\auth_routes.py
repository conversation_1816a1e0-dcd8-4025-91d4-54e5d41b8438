"""
Authentication Routes

Đ<PERSON>y là file chứa tất cả API endpoints liên quan đến authentication:
- Register (đăng ký)
- <PERSON>gin (đăng nhập)  
- <PERSON><PERSON>ut (đăng xuất)
- Token refresh
- Password reset
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import (
    create_access_token, 
    create_refresh_token,
    jwt_required, 
    get_jwt_identity,
    get_jwt
)
from marshmallow import Schema, fields, ValidationError
from app import db
from app.models.user import User
from app.services.auth_service import AuthService

# Tạo Blueprint cho auth routes
# Blueprint cho phép tổ chức routes thành modules
auth_bp = Blueprint('auth', __name__)

# Khởi tạo auth service
auth_service = AuthService()


class RegisterSchema(Schema):
    """
    Schema validation cho register endpoint
    Marshmallow schema để validate input data
    """
    username = fields.Str(required=True, validate=lambda x: len(x) >= 3)
    email = fields.Email(required=True)
    password = fields.Str(required=True, validate=lambda x: len(x) >= 6)
    first_name = fields.Str(required=False)
    last_name = fields.Str(required=False)
    phone = fields.Str(required=False)


class LoginSchema(Schema):
    """
    Schema validation cho login endpoint
    """
    username = fields.Str(required=True)
    password = fields.Str(required=True)


@auth_bp.route('/register', methods=['POST'])
def register():
    """
    User Registration Endpoint
    
    POST /api/auth/register
    
    Body:
    {
        "username": "john_doe",
        "email": "<EMAIL>", 
        "password": "password123",
        "first_name": "John",
        "last_name": "Doe",
        "phone": "0123456789"
    }
    
    Returns:
        201: User created successfully
        400: Validation error hoặc user đã tồn tại
        500: Server error
    """
    try:
        # Parse và validate JSON data
        schema = RegisterSchema()
        data = schema.load(request.json)
        
    except ValidationError as err:
        # Trả về lỗi validation
        return jsonify({
            'error': 'Validation failed',
            'messages': err.messages
        }), 400
    
    try:
        # Kiểm tra user đã tồn tại chưa
        if User.find_by_username(data['username']):
            return jsonify({
                'error': 'Username already exists'
            }), 400
            
        if User.find_by_email(data['email']):
            return jsonify({
                'error': 'Email already exists'
            }), 400
        
        # Tạo user mới
        user = User(
            username=data['username'],
            email=data['email'],
            password=data['password'],
            first_name=data.get('first_name'),
            last_name=data.get('last_name'),
            phone=data.get('phone')
        )
        
        # Lưu vào database
        db.session.add(user)
        db.session.commit()
        
        # Tạo JWT tokens
        access_token = create_access_token(identity=user.id)
        refresh_token = create_refresh_token(identity=user.id)
        
        return jsonify({
            'message': 'User registered successfully',
            'user': user.to_dict(),
            'access_token': access_token,
            'refresh_token': refresh_token
        }), 201
        
    except Exception as e:
        # Rollback transaction nếu có lỗi
        db.session.rollback()
        return jsonify({
            'error': 'Registration failed',
            'message': str(e)
        }), 500


@auth_bp.route('/login', methods=['POST'])
def login():
    """
    User Login Endpoint
    
    POST /api/auth/login
    
    Body:
    {
        "username": "john_doe",
        "password": "password123"
    }
    
    Returns:
        200: Login successful với tokens
        401: Invalid credentials
        400: Validation error
    """
    try:
        # Parse và validate JSON data
        schema = LoginSchema()
        data = schema.load(request.json)
        
    except ValidationError as err:
        return jsonify({
            'error': 'Validation failed',
            'messages': err.messages
        }), 400
    
    # Tìm user theo username hoặc email
    user = User.find_by_username(data['username']) or User.find_by_email(data['username'])
    
    # Kiểm tra user tồn tại và password đúng
    if user and user.check_password(data['password']):
        # Kiểm tra account có active không
        if not user.is_active:
            return jsonify({
                'error': 'Account is deactivated'
            }), 401
        
        # Cập nhật last login
        user.update_last_login()
        
        # Tạo JWT tokens
        access_token = create_access_token(identity=user.id)
        refresh_token = create_refresh_token(identity=user.id)
        
        return jsonify({
            'message': 'Login successful',
            'user': user.to_dict(),
            'access_token': access_token,
            'refresh_token': refresh_token
        }), 200
    
    else:
        return jsonify({
            'error': 'Invalid username or password'
        }), 401


@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """
    Token Refresh Endpoint
    
    POST /api/auth/refresh
    Headers: Authorization: Bearer <refresh_token>
    
    Returns:
        200: New access token
        401: Invalid refresh token
    """
    # Lấy user ID từ refresh token
    current_user_id = get_jwt_identity()
    
    # Tạo access token mới
    new_access_token = create_access_token(identity=current_user_id)
    
    return jsonify({
        'access_token': new_access_token
    }), 200


@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """
    User Logout Endpoint
    
    POST /api/auth/logout
    Headers: Authorization: Bearer <access_token>
    
    Note: Trong implementation đơn giản này, chúng ta chỉ trả về success message.
    Trong production, bạn có thể implement token blacklisting.
    
    Returns:
        200: Logout successful
    """
    return jsonify({
        'message': 'Logout successful'
    }), 200


@auth_bp.route('/me', methods=['GET'])
@jwt_required()
def get_current_user():
    """
    Get Current User Profile Endpoint
    
    GET /api/auth/me
    Headers: Authorization: Bearer <access_token>
    
    Returns:
        200: User profile data
        404: User not found
    """
    # Lấy user ID từ JWT token
    current_user_id = get_jwt_identity()
    
    # Tìm user trong database
    user = User.find_by_id(current_user_id)
    
    if user:
        return jsonify({
            'user': user.to_dict()
        }), 200
    else:
        return jsonify({
            'error': 'User not found'
        }), 404
