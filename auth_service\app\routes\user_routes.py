"""
User Management Routes

Đ<PERSON>y là file chứa các API endpoints để quản lý user:
- Get user profile
- Update user profile  
- Change password
- Deactivate account
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from marshmallow import Schema, fields, ValidationError
from app import db
from app.models.user import User

# Tạo Blueprint cho user routes
user_bp = Blueprint('users', __name__)


class UpdateProfileSchema(Schema):
    """
    Schema validation cho update profile endpoint
    """
    first_name = fields.Str(required=False)
    last_name = fields.Str(required=False)
    phone = fields.Str(required=False)
    email = fields.Email(required=False)


class ChangePasswordSchema(Schema):
    """
    Schema validation cho change password endpoint
    """
    current_password = fields.Str(required=True)
    new_password = fields.Str(required=True, validate=lambda x: len(x) >= 6)


@user_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """
    Get User Profile Endpoint
    
    GET /api/users/profile
    Headers: Authorization: Bearer <access_token>
    
    Returns:
        200: User profile data
        404: User not found
    """
    current_user_id = get_jwt_identity()
    user = User.find_by_id(current_user_id)
    
    if user:
        return jsonify({
            'user': user.to_dict()
        }), 200
    else:
        return jsonify({
            'error': 'User not found'
        }), 404


@user_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """
    Update User Profile Endpoint
    
    PUT /api/users/profile
    Headers: Authorization: Bearer <access_token>
    
    Body:
    {
        "first_name": "John",
        "last_name": "Doe",
        "phone": "0123456789",
        "email": "<EMAIL>"
    }
    
    Returns:
        200: Profile updated successfully
        400: Validation error
        404: User not found
    """
    try:
        # Parse và validate JSON data
        schema = UpdateProfileSchema()
        data = schema.load(request.json)
        
    except ValidationError as err:
        return jsonify({
            'error': 'Validation failed',
            'messages': err.messages
        }), 400
    
    current_user_id = get_jwt_identity()
    user = User.find_by_id(current_user_id)
    
    if not user:
        return jsonify({
            'error': 'User not found'
        }), 404
    
    try:
        # Kiểm tra email mới có bị trùng không (nếu có thay đổi email)
        if 'email' in data and data['email'] != user.email:
            existing_user = User.find_by_email(data['email'])
            if existing_user:
                return jsonify({
                    'error': 'Email already exists'
                }), 400
        
        # Cập nhật các field
        for field, value in data.items():
            if hasattr(user, field):
                setattr(user, field, value)
        
        # Lưu changes
        db.session.commit()
        
        return jsonify({
            'message': 'Profile updated successfully',
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'error': 'Update failed',
            'message': str(e)
        }), 500


@user_bp.route('/change-password', methods=['POST'])
@jwt_required()
def change_password():
    """
    Change Password Endpoint
    
    POST /api/users/change-password
    Headers: Authorization: Bearer <access_token>
    
    Body:
    {
        "current_password": "oldpassword",
        "new_password": "newpassword123"
    }
    
    Returns:
        200: Password changed successfully
        400: Validation error hoặc current password sai
        404: User not found
    """
    try:
        # Parse và validate JSON data
        schema = ChangePasswordSchema()
        data = schema.load(request.json)
        
    except ValidationError as err:
        return jsonify({
            'error': 'Validation failed',
            'messages': err.messages
        }), 400
    
    current_user_id = get_jwt_identity()
    user = User.find_by_id(current_user_id)
    
    if not user:
        return jsonify({
            'error': 'User not found'
        }), 404
    
    # Kiểm tra current password có đúng không
    if not user.check_password(data['current_password']):
        return jsonify({
            'error': 'Current password is incorrect'
        }), 400
    
    try:
        # Set password mới
        user.set_password(data['new_password'])
        db.session.commit()
        
        return jsonify({
            'message': 'Password changed successfully'
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'error': 'Password change failed',
            'message': str(e)
        }), 500


@user_bp.route('/deactivate', methods=['POST'])
@jwt_required()
def deactivate_account():
    """
    Deactivate Account Endpoint
    
    POST /api/users/deactivate
    Headers: Authorization: Bearer <access_token>
    
    Returns:
        200: Account deactivated successfully
        404: User not found
    """
    current_user_id = get_jwt_identity()
    user = User.find_by_id(current_user_id)
    
    if not user:
        return jsonify({
            'error': 'User not found'
        }), 404
    
    try:
        # Set account thành inactive
        user.is_active = False
        db.session.commit()
        
        return jsonify({
            'message': 'Account deactivated successfully'
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'error': 'Deactivation failed',
            'message': str(e)
        }), 500
