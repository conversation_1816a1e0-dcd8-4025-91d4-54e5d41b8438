"""
Authentication Middleware

Đ<PERSON><PERSON> là file chứa các middleware functions cho authentication và authorization.
Middleware chạy trướ<PERSON> khi request đến route handlers.
"""

from functools import wraps
from flask import request, jsonify, g
from flask_jwt_extended import verify_jwt_in_request, get_jwt_identity, get_jwt
from app.models.user import User


def require_auth(f):
    """
    Middleware decorator để require authentication
    
    Usage:
    @require_auth
    def protected_endpoint():
        # Access current user via g.current_user
        pass
    
    Args:
        f (function): Function được protect
        
    Returns:
        function: Decorated function
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Verify JWT token có hợp lệ không
            verify_jwt_in_request()
            
            # Get user ID từ token
            current_user_id = get_jwt_identity()
            
            # Load user từ database
            user = User.find_by_id(current_user_id)
            
            if not user:
                return jsonify({
                    'error': 'User not found',
                    'code': 'USER_NOT_FOUND'
                }), 404
            
            if not user.is_active:
                return jsonify({
                    'error': 'Account is deactivated',
                    'code': 'ACCOUNT_DEACTIVATED'
                }), 401
            
            # Store user trong Flask's g object để access trong route
            g.current_user = user
            
            return f(*args, **kwargs)
            
        except Exception as e:
            return jsonify({
                'error': 'Authentication required',
                'code': 'AUTH_REQUIRED',
                'message': str(e)
            }), 401
    
    return decorated_function


def require_verified_user(f):
    """
    Middleware decorator để require verified user
    
    Args:
        f (function): Function được protect
        
    Returns:
        function: Decorated function
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # First check authentication
        try:
            verify_jwt_in_request()
            current_user_id = get_jwt_identity()
            user = User.find_by_id(current_user_id)
            
            if not user or not user.is_active:
                return jsonify({
                    'error': 'Authentication required',
                    'code': 'AUTH_REQUIRED'
                }), 401
            
            # Check verification status
            if not user.is_verified:
                return jsonify({
                    'error': 'Email verification required',
                    'code': 'VERIFICATION_REQUIRED'
                }), 403
            
            g.current_user = user
            return f(*args, **kwargs)
            
        except Exception as e:
            return jsonify({
                'error': 'Authentication required',
                'code': 'AUTH_REQUIRED',
                'message': str(e)
            }), 401
    
    return decorated_function


def rate_limit_middleware():
    """
    Basic rate limiting middleware
    
    Note: Đây là implementation đơn giản. Trong production,
    sử dụng Redis hoặc external rate limiting service.
    """
    # Get client IP
    client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
    
    # Basic rate limiting logic
    # Trong production, implement với Redis
    # rate_limit_key = f'rate_limit:{client_ip}'
    # current_requests = redis_client.get(rate_limit_key) or 0
    
    # if int(current_requests) > 100:  # 100 requests per minute
    #     return jsonify({
    #         'error': 'Rate limit exceeded',
    #         'code': 'RATE_LIMIT_EXCEEDED'
    #     }), 429
    
    pass


def cors_middleware():
    """
    CORS middleware để handle cross-origin requests
    
    Note: Flask-CORS extension đã handle CORS,
    nhưng đây là example cho custom CORS handling.
    """
    def after_request(response):
        # Set CORS headers
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
        return response
    
    return after_request


def request_logging_middleware():
    """
    Middleware để log requests
    """
    import logging
    from datetime import datetime
    
    # Setup logger
    logger = logging.getLogger('auth_service')
    
    def log_request():
        # Log request info
        logger.info(f'{datetime.utcnow()} - {request.method} {request.path} - IP: {request.remote_addr}')
    
    return log_request


def security_headers_middleware():
    """
    Middleware để add security headers
    """
    def after_request(response):
        # Security headers
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        return response
    
    return after_request


def validate_content_type(required_type='application/json'):
    """
    Middleware decorator để validate Content-Type header
    
    Args:
        required_type (str): Required content type
        
    Returns:
        function: Decorator function
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if request.method in ['POST', 'PUT', 'PATCH']:
                if not request.is_json:
                    return jsonify({
                        'error': f'Content-Type must be {required_type}',
                        'code': 'INVALID_CONTENT_TYPE'
                    }), 400
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator


def handle_options_request():
    """
    Handle preflight OPTIONS requests
    """
    if request.method == 'OPTIONS':
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
        return response
