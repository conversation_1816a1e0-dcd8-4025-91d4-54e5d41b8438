"""
Authentication Service

Đ<PERSON>y là service layer chứa business logic cho authentication.
Service layer tách biệt business logic khỏi routes, giúp code dễ test và maintain.
"""

from datetime import datetime, timedelta
from flask_jwt_extended import create_access_token, create_refresh_token
from app import db
from app.models.user import User


class AuthService:
    """
    Authentication Service Class
    
    Chứa các methods để xử lý authentication logic.
    """
    
    def __init__(self):
        """
        Constructor cho AuthService
        """
        pass
    
    def register_user(self, user_data):
        """
        Đăng ký user mới
        
        Args:
            user_data (dict): Dictionary chứa thông tin user
            
        Returns:
            tuple: (success: bool, result: dict/str)
        """
        try:
            # Kiểm tra user đã tồn tại chưa
            if User.find_by_username(user_data['username']):
                return False, 'Username already exists'
                
            if User.find_by_email(user_data['email']):
                return False, 'Email already exists'
            
            # Tạo user mới
            user = User(
                username=user_data['username'],
                email=user_data['email'],
                password=user_data['password'],
                first_name=user_data.get('first_name'),
                last_name=user_data.get('last_name'),
                phone=user_data.get('phone')
            )
            
            # Lưu vào database
            db.session.add(user)
            db.session.commit()
            
            # Tạo tokens
            tokens = self.generate_tokens(user.id)
            
            return True, {
                'user': user.to_dict(),
                'tokens': tokens
            }
            
        except Exception as e:
            db.session.rollback()
            return False, f'Registration failed: {str(e)}'
    
    def authenticate_user(self, username, password):
        """
        Xác thực user credentials
        
        Args:
            username (str): Username hoặc email
            password (str): Plain text password
            
        Returns:
            tuple: (success: bool, result: dict/str)
        """
        try:
            # Tìm user theo username hoặc email
            user = User.find_by_username(username) or User.find_by_email(username)
            
            # Kiểm tra user tồn tại và password đúng
            if not user or not user.check_password(password):
                return False, 'Invalid username or password'
            
            # Kiểm tra account có active không
            if not user.is_active:
                return False, 'Account is deactivated'
            
            # Cập nhật last login
            user.update_last_login()
            
            # Tạo tokens
            tokens = self.generate_tokens(user.id)
            
            return True, {
                'user': user.to_dict(),
                'tokens': tokens
            }
            
        except Exception as e:
            return False, f'Authentication failed: {str(e)}'
    
    def generate_tokens(self, user_id):
        """
        Tạo JWT access và refresh tokens
        
        Args:
            user_id (int): User ID
            
        Returns:
            dict: Dictionary chứa access_token và refresh_token
        """
        access_token = create_access_token(identity=user_id)
        refresh_token = create_refresh_token(identity=user_id)
        
        return {
            'access_token': access_token,
            'refresh_token': refresh_token
        }
    
    def refresh_access_token(self, user_id):
        """
        Tạo access token mới từ refresh token
        
        Args:
            user_id (int): User ID từ refresh token
            
        Returns:
            str: New access token
        """
        return create_access_token(identity=user_id)
    
    def validate_user_exists(self, user_id):
        """
        Kiểm tra user có tồn tại và active không
        
        Args:
            user_id (int): User ID
            
        Returns:
            tuple: (exists: bool, user: User/None)
        """
        user = User.find_by_id(user_id)
        
        if user and user.is_active:
            return True, user
        else:
            return False, None
    
    def change_user_password(self, user_id, current_password, new_password):
        """
        Thay đổi password của user
        
        Args:
            user_id (int): User ID
            current_password (str): Password hiện tại
            new_password (str): Password mới
            
        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            user = User.find_by_id(user_id)
            
            if not user:
                return False, 'User not found'
            
            # Kiểm tra current password
            if not user.check_password(current_password):
                return False, 'Current password is incorrect'
            
            # Set password mới
            user.set_password(new_password)
            db.session.commit()
            
            return True, 'Password changed successfully'
            
        except Exception as e:
            db.session.rollback()
            return False, f'Password change failed: {str(e)}'
    
    def deactivate_user(self, user_id):
        """
        Deactivate user account
        
        Args:
            user_id (int): User ID
            
        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            user = User.find_by_id(user_id)
            
            if not user:
                return False, 'User not found'
            
            user.is_active = False
            db.session.commit()
            
            return True, 'Account deactivated successfully'
            
        except Exception as e:
            db.session.rollback()
            return False, f'Deactivation failed: {str(e)}'
