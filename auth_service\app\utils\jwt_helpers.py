"""
JWT Helper Functions

Đây là file chứa các utility functions để làm việc với JWT tokens.
"""

from functools import wraps
from flask import jsonify
from flask_jwt_extended import verify_jwt_in_request, get_jwt_identity, get_jwt
from app.models.user import User


def admin_required(f):
    """
    Decorator để require admin role
    
    Usage:
    @admin_required
    def admin_only_endpoint():
        pass
    
    Args:
        f (function): Function được decorate
        
    Returns:
        function: Decorated function
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Verify JWT token
        verify_jwt_in_request()
        
        # Get current user
        current_user_id = get_jwt_identity()
        user = User.find_by_id(current_user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        if user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403
        
        return f(*args, **kwargs)
    
    return decorated_function


def role_required(required_roles):
    """
    Decorator để require specific roles
    
    Usage:
    @role_required(['admin', 'moderator'])
    def moderator_or_admin_endpoint():
        pass
    
    Args:
        required_roles (list): List các roles được phép access
        
    Returns:
        function: Decorator function
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Verify JWT token
            verify_jwt_in_request()
            
            # Get current user
            current_user_id = get_jwt_identity()
            user = User.find_by_id(current_user_id)
            
            if not user:
                return jsonify({'error': 'User not found'}), 404
            
            if user.role not in required_roles:
                return jsonify({
                    'error': f'Access denied. Required roles: {", ".join(required_roles)}'
                }), 403
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator


def get_current_user():
    """
    Helper function để get current user từ JWT token
    
    Returns:
        User: Current user object hoặc None
    """
    try:
        current_user_id = get_jwt_identity()
        return User.find_by_id(current_user_id)
    except:
        return None


def is_token_revoked(jwt_header, jwt_payload):
    """
    Callback function để check token có bị revoke không
    
    Note: Đây là basic implementation. Trong production,
    bạn có thể implement token blacklisting với Redis.
    
    Args:
        jwt_header (dict): JWT header
        jwt_payload (dict): JWT payload
        
    Returns:
        bool: True nếu token bị revoke
    """
    # Basic implementation - không revoke token nào
    # Trong production, check token trong blacklist
    return False


def get_user_claims(user):
    """
    Tạo custom claims cho JWT token
    
    Args:
        user (User): User object
        
    Returns:
        dict: Custom claims
    """
    return {
        'role': user.role,
        'is_verified': user.is_verified,
        'username': user.username
    }


def verify_token_not_blacklisted(jwt_header, jwt_payload):
    """
    Verify token không bị blacklist
    
    Args:
        jwt_header (dict): JWT header
        jwt_payload (dict): JWT payload
        
    Returns:
        bool: True nếu token valid
    """
    # Implementation cho token blacklisting
    # Có thể sử dụng Redis để store blacklisted tokens
    jti = jwt_payload['jti']  # JWT ID
    
    # Check trong blacklist (Redis example)
    # from app import redis_client
    # return redis_client.get(f'blacklist:{jti}') is None
    
    return True  # Basic implementation


def add_token_to_blacklist(jti):
    """
    Add token vào blacklist
    
    Args:
        jti (str): JWT ID
    """
    # Implementation để add token vào blacklist
    # Redis example:
    # from app import redis_client
    # redis_client.set(f'blacklist:{jti}', 'true', ex=3600)  # Expire after 1 hour
    pass


def create_user_tokens(user):
    """
    Tạo access và refresh tokens cho user với custom claims
    
    Args:
        user (User): User object
        
    Returns:
        dict: Dictionary chứa tokens
    """
    from flask_jwt_extended import create_access_token, create_refresh_token
    
    # Custom claims
    additional_claims = get_user_claims(user)
    
    # Tạo tokens
    access_token = create_access_token(
        identity=user.id,
        additional_claims=additional_claims
    )
    
    refresh_token = create_refresh_token(identity=user.id)
    
    return {
        'access_token': access_token,
        'refresh_token': refresh_token
    }


def extract_token_info():
    """
    Extract thông tin từ current JWT token
    
    Returns:
        dict: Token information
    """
    try:
        claims = get_jwt()
        user_id = get_jwt_identity()
        
        return {
            'user_id': user_id,
            'role': claims.get('role'),
            'is_verified': claims.get('is_verified'),
            'username': claims.get('username'),
            'jti': claims.get('jti'),
            'exp': claims.get('exp'),
            'iat': claims.get('iat')
        }
    except:
        return None
