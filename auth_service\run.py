"""
Application Entry Point

Đây là file chính để chạy Flask application.
File này import app từ app factory và chạy development server.
"""

from app import create_app
import os

# Tạo app instance sử dụng app factory
app = create_app()

if __name__ == '__main__':
    """
    Chạy development server khi file được execute trực tiếp
    
    Chú ý: Trong production, không nên sử dụng Flask's built-in server.
    <PERSON><PERSON> vào đó, sử dụng WSGI server như Gunicorn hoặc uWSGI.
    """
    
    # Lấy cấu hình từ environment variables
    host = os.environ.get('FLASK_HOST', '127.0.0.1')  # Default localhost
    port = int(os.environ.get('FLASK_PORT', 5000))    # Default port 5000
    debug = os.environ.get('FLASK_ENV') == 'development'  # Debug mode
    
    print(f"🚀 Starting Auth Service on http://{host}:{port}")
    print(f"📝 Debug mode: {debug}")
    
    # Chạy Flask development server
    app.run(
        host=host,
        port=port,
        debug=debug
    )
