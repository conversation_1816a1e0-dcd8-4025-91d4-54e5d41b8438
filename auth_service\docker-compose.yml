version: '3.8'

services:
  # Auth Service
  auth_service:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=**************************************************/auth_db
      - SECRET_KEY=your-secret-key-here
      - JWT_SECRET_KEY=your-jwt-secret-key-here
      - CORS_ORIGINS=http://localhost:3000,http://localhost:8080
    depends_on:
      - postgres
      - redis
    volumes:
      - .:/app
    networks:
      - auth_network

  # PostgreSQL Database
  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=auth_db
      - POSTGRES_USER=auth_user
      - POSTGRES_PASSWORD=auth_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - auth_network

  # Redis (cho caching và rate limiting)
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - auth_network

  # pgAdmin (Database management tool)
  pgadmin:
    image: dpage/pgadmin4
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
    ports:
      - "8080:80"
    depends_on:
      - postgres
    networks:
      - auth_network

volumes:
  postgres_data:
  redis_data:

networks:
  auth_network:
    driver: bridge
