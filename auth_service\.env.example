# Environment Variables Template
# Copy file này thành .env và điền các giá trị thực tế

# Flask Configuration
FLASK_APP=run.py
FLASK_ENV=development
FLASK_HOST=127.0.0.1
FLASK_PORT=5000

# Security Keys
# QUAN TRỌNG: Generate random keys cho production!
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# Database Configuration
# SQLite (Development)
DATABASE_URL=sqlite:///auth_service.db

# SQL Server (Production example)
# DATABASE_URL=mssql+pyodbc://username:password@server/database?driver=ODBC+Driver+17+for+SQL+Server

# PostgreSQL (Production example)
# DATABASE_URL=postgresql://username:password@localhost/auth_service

# MySQL (Production example)
# DATABASE_URL=mysql+pymysql://username:password@localhost/auth_service

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Email Configuration (cho password reset, verification)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Logging Level
LOG_LEVEL=INFO
