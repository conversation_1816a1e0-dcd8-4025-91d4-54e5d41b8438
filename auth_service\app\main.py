# TODO: Implement FastAPI main application
# Xem hướng dẫn trong FASTAPI_GUIDE.md
from fastapi import FastAPI

app = FastAPI(
    title="Auth Service API",
    description="Authentication and Authorization Service",
    version="1.0.0"
)

@app.get("/")
async def root():
    return {"message": "Auth Service is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "auth_service"}